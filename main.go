package main

import (
	"fmt"
	"log"

	"net-interceptor/interceptor"
	"net-interceptor/matcher"
	"net-interceptor/model"
	"net-interceptor/parser"
)

func main() {
	// 初始化数据库
	db, err := model.InitDB("packets.db")
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 加载匹配规则
	rules, err := matcher.LoadRules("config/rules.json")
	if err != nil {
		log.Fatalf("加载规则失败: %v", err)
	}

	// 启动网络拦截器
	interceptor.Start(func(raw []byte, meta *interceptor.PacketMetadata) {
		// 解析数据包
		parserPacket := parser.Parse(meta, raw)
		if parserPacket == nil {
			return
		}
		fmt.Println("解析数据包", parserPacket)
		// 添加调试日志
		if len(parserPacket.Payload) > 0 {
			log.Printf("🔍 解析到payload: %s:%d -> %s:%d, 内容长度: %d",
				parserPacket.SrcIP, parserPacket.SrcPort,
				parserPacket.DstIP, parserPacket.DstPort,
				len(parserPacket.Payload))

			// 如果是文本内容，显示前100个字符
			if parserPacket.Payload != "[binary]" && len(parserPacket.Payload) > 0 {
				preview := parserPacket.Payload
				if len(preview) > 100 {
					preview = preview[:100] + "..."
				}
				log.Printf("📄 Payload预览: %s", preview)
			}
		}

		// 匹配规则
		if matcher.Match(parserPacket.Payload, rules) {
			log.Printf("✅ 命中规则：%s:%d -> %s:%d",
				parserPacket.SrcIP, parserPacket.SrcPort,
				parserPacket.DstIP, parserPacket.DstPort)

			// 转换为模型结构
			packet := &model.Packet{
				SrcIp:   parserPacket.SrcIP,
				DstIp:   parserPacket.DstIP,
				SrcPort: parserPacket.SrcPort,
				DstPort: parserPacket.DstPort,
				Payload: parserPacket.Payload,
			}

			if err := packetModel.Create(packet); err != nil {
				log.Printf("❌ 保存数据包失败: %v", err)
			} else {
				log.Printf("💾 数据包已保存到数据库")
			}
		}
	})
}
