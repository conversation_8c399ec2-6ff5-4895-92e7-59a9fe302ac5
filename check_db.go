package main

import (
	"fmt"
	"log"
	"strings"

	"net-interceptor/model"
)

func main() {
	// 连接数据库
	db, err := model.InitDB("packets.db")
	if err != nil {
		log.Fatalf("数据库连接失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 直接查询所有数据包
	packetList, _, err := packetModel.List(model.PacketReq{})
	if err != nil {
		log.Fatalf("查询失败: %v", err)
	}
	total := int64(len(packetList))

	fmt.Printf("========================================\n")
	fmt.Printf("           数据库查询结果\n")
	fmt.Printf("========================================\n")
	fmt.Printf("总共找到 %d 条记录\n\n", total)

	if total == 0 {
		fmt.Println("❌ 数据库中没有找到任何数据包记录")
		fmt.Println("\n可能的原因：")
		fmt.Println("1. 拦截器没有捕获到匹配的流量")
		fmt.Println("2. 规则匹配失败")
		fmt.Println("3. 数据保存失败")
		fmt.Println("\n建议：")
		fmt.Println("1. 检查拦截器日志输出")
		fmt.Println("2. 确认规则配置正确")
		fmt.Println("3. 生成包含关键词的网络流量")
	} else {
		fmt.Println("✅ 找到以下数据包记录：")
		fmt.Println()

		for i, packet := range packetList {
			fmt.Printf("记录 %d:\n", i+1)
			fmt.Printf("  ID: %d\n", packet.Id)
			fmt.Printf("  源地址: %s:%d\n", packet.SrcIp, packet.SrcPort)
			fmt.Printf("  目标地址: %s:%d\n", packet.DstIp, packet.DstPort)
			fmt.Printf("  时间: %s\n", packet.Timestamp.Format("2006-01-02 15:04:05"))

			// 显示payload预览
			payload := packet.Payload
			if len(payload) > 200 {
				payload = payload[:200] + "..."
			}
			fmt.Printf("  内容: %s\n", payload)
			fmt.Println("  " + strings.Repeat("=", 50))
		}
	}

	fmt.Printf("\n========================================\n")
}
