package matcher

import (
	"encoding/json"
	"io/ioutil"
	"log"
	"strings"

	"net-interceptor/parser"
)

type Rules struct {
	Keywords []string `json:"keywords"`
}

func LoadRules(path string) (*Rules, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var r Rules
	if err := json.Unmarshal(data, &r); err != nil {
		return nil, err
	}
	log.Printf("规则已加载：%v", r.Keywords)
	return &r, nil
}

func Match(payload string, rules *Rules) bool {
	if len(payload) == 0 {
		return false
	}

	for _, kw := range rules.Keywords {
		if strings.Contains(payload, kw) {
			log.Printf("🎯 匹配成功！关键词: %s", kw)
			return true
		}
	}
	return false
}
