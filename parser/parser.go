package parser

import (
	"net-interceptor/interceptor"
)

type Packet struct {
	SrcIP   string
	DstIP   string
	SrcPort uint16
	DstPort uint16
	Payload string
}

func Parse(meta *interceptor.PacketMetadata, raw []byte) *Packet {
	// 解析 TCP payload
	payload := extractTCPPayload(raw)

	return &Packet{
		SrcIP:   meta.SrcIP,
		DstIP:   meta.DstIP,
		SrcPort: meta.SrcPort,
		DstPort: meta.DstPort,
		Payload: payload,
	}
}

// extractTCPPayload 从原始数据包中提取TCP payload
func extractTCPPayload(raw []byte) string {
	if len(raw) < 20 {
		return ""
	}

	// 解析IP头部长度
	ipHeaderLen := int(raw[0]&0x0F) * 4
	if ipHeaderLen < 20 || len(raw) < ipHeaderLen {
		return ""
	}

	// 检查是否是TCP协议 (协议号6)
	if raw[9] != 6 {
		return ""
	}

	// 检查TCP头部
	if len(raw) < ipHeaderLen+20 {
		return ""
	}

	// 解析TCP头部长度
	tcpHeaderLen := int(raw[ipHeaderLen+12]>>4) * 4
	if tcpHeaderLen < 20 {
		return ""
	}

	// 计算payload起始位置
	payloadStart := ipHeaderLen + tcpHeaderLen
	if payloadStart >= len(raw) {
		return ""
	}

	// 提取TCP payload
	payload := raw[payloadStart:]
	if len(payload) == 0 {
		return ""
	}

	// 转换为字符串
	return extractPayload(payload)
}

func extractPayload(raw []byte) string {
	if len(raw) == 0 {
		return ""
	}
	// 尝试只提取文本内容
	if isPrintable(raw) {
		return string(raw)
	}
	return "[binary]"
}

func isPrintable(data []byte) bool {
	for _, b := range data {
		if b < 32 && b != 9 && b != 10 && b != 13 {
			return false
		}
	}
	return true
}
