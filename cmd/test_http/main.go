package main

import (
	"fmt"
	"net/http"
	"time"
)

func main() {
	fmt.Println("========================================")
	fmt.Println("           HTTP测试客户端")
	fmt.Println("========================================")
	fmt.Println()

	// 测试目标域名
	testURLs := []string{
		"http://ad.oceanengine.com",
		"http://ad.oceanengine.com/api/test",
		"http://www.baidu.com", // 对照组
	}

	client := &http.Client{
		Timeout: 5 * time.Second,
	}

	for i, url := range testURLs {
		fmt.Printf("测试 %d: 请求 %s\n", i+1, url)

		req, err := http.NewRequest("GET", url, nil)
		if err != nil {
			fmt.Printf("  ❌ 创建请求失败: %v\n", err)
			continue
		}

		// 添加自定义头部
		req.Header.Set("User-Agent", "TestClient/1.0")
		req.Header.Set("Accept", "text/html,application/xhtml+xml")

		// 发送请求
		resp, err := client.Do(req)
		if err != nil {
			fmt.Printf("  ⚠️ 请求失败: %v (这是正常的，重要的是发送了HTTP请求)\n", err)
		} else {
			fmt.Printf("  ✅ 请求成功，状态码: %d\n", resp.StatusCode)
			resp.Body.Close()
		}

		fmt.Println()
		time.Sleep(1 * time.Second) // 间隔1秒
	}

	fmt.Println("========================================")
	fmt.Println("HTTP测试完成！请检查拦截器是否捕获到相关流量")
	fmt.Println("========================================")
}
