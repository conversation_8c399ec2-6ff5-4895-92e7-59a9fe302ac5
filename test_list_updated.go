package main

import (
	"log"
	"time"

	"net-interceptor/model"
)

func main() {
	// 测试数据库连接和模型
	db, err := model.InitDB("test_list_updated.db")
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 创建测试数据
	testPackets := []*model.Packet{
		{
			SrcIp:     "*************",
			DstIp:     "*******",
			SrcPort:   12345,
			DstPort:   80,
			Payload:   "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n",
			Timestamp: time.Now(),
		},
		{
			SrcIp:     "*************",
			DstIp:     "*******",
			SrcPort:   12346,
			DstPort:   443,
			Payload:   "GET /api HTTP/1.1\r\nHost: api.example.com\r\n\r\n",
			Timestamp: time.Now(),
		},
		{
			SrcIp:     "*************",
			DstIp:     "*******",
			SrcPort:   12347,
			DstPort:   80,
			Payload:   "POST /data HTTP/1.1\r\nHost: data.example.com\r\n\r\n",
			Timestamp: time.Now(),
		},
	}

	// 批量插入测试数据
	for i, packet := range testPackets {
		if err := packetModel.Create(packet); err != nil {
			log.Fatalf("创建第%d个数据包失败: %v", i+1, err)
		}
		log.Printf("✅ 创建数据包 %d 成功，ID: %d", i+1, packet.Id)
	}

	// 测试1: 不分页查询所有数据（使用零值）
	log.Println("\n=== 测试1: 不分页查询所有数据（使用零值） ===")
	req1 := model.PacketListRequest{} // 所有字段都是零值
	packets, total, err := packetModel.List(req1)
	if err != nil {
		log.Fatalf("查询失败: %v", err)
	}
	log.Printf("✅ 查询成功，返回 %d 条数据，total: %d", len(packets), total)

	// 测试2: 分页查询
	log.Println("\n=== 测试2: 分页查询 (第1页，每页2条) ===")
	req2 := model.PacketListRequest{
		Page:     1,
		PageSize: 2,
	}
	packets, total, err = packetModel.List(req2)
	if err != nil {
		log.Fatalf("分页查询失败: %v", err)
	}
	log.Printf("✅ 分页查询成功，返回 %d 条数据，total: %d", len(packets), total)

	// 测试3: 条件查询 + 分页
	log.Println("\n=== 测试3: 条件查询 + 分页 ===")
	req3 := model.PacketListRequest{
		SrcIp:    "*************",
		Page:     1,
		PageSize: 10,
	}
	packets, total, err = packetModel.List(req3)
	if err != nil {
		log.Fatalf("条件查询失败: %v", err)
	}
	log.Printf("✅ 条件查询成功，SrcIp=%s，返回 %d 条数据，total: %d", req3.SrcIp, len(packets), total)

	// 测试4: 模糊查询
	log.Println("\n=== 测试4: 模糊查询 ===")
	req4 := model.PacketListRequest{
		Payload: "HTTP",
	}
	packets, total, err = packetModel.List(req4)
	if err != nil {
		log.Fatalf("模糊查询失败: %v", err)
	}
	log.Printf("✅ 模糊查询成功，Payload包含'%s'，返回 %d 条数据，total: %d", req4.Payload, len(packets), total)

	// 测试5: 多条件查询
	log.Println("\n=== 测试5: 多条件查询 ===")
	req5 := model.PacketListRequest{
		SrcIp:   "*************",
		DstPort: 80,
	}
	packets, total, err = packetModel.List(req5)
	if err != nil {
		log.Fatalf("多条件查询失败: %v", err)
	}
	log.Printf("✅ 多条件查询成功，SrcIp=%s AND DstPort=%d，返回 %d 条数据，total: %d", req5.SrcIp, req5.DstPort, len(packets), total)

	// 测试6: 验证零值不会被当作查询条件
	log.Println("\n=== 测试6: 验证零值不会被当作查询条件 ===")
	req6 := model.PacketListRequest{
		Id:      0,     // 零值，不应该被当作查询条件
		SrcPort: 0,     // 零值，不应该被当作查询条件
		SrcIp:   "*************", // 非零值，应该被当作查询条件
	}
	packets, total, err = packetModel.List(req6)
	if err != nil {
		log.Fatalf("零值测试失败: %v", err)
	}
	log.Printf("✅ 零值测试成功，只有SrcIp条件生效，返回 %d 条数据，total: %d", len(packets), total)

	log.Println("\n🎉 所有测试通过！更新后的List方法工作正常")
}
