package interceptor

/*
#cgo CFLAGS: -I${SRCDIR}/../lib
#cgo LDFLAGS: -L${SRCDIR}/../lib -lwindivert
#include <windows.h>
#include <stdlib.h>
#include "windivert.h"

// 包装函数，确保句柄正确传递
static HANDLE safe_windivert_open(const char* filter, int layer, int priority, int flags) {
    return WinDivertOpen(filter, layer, priority, flags);
}

static BOOL safe_windivert_recv(HANDLE handle, void* packet, UINT packet_len, UINT* recv_len, void* addr) {
    return WinDivertRecv(handle, packet, packet_len, recv_len, (PWINDIVERT_ADDRESS)addr);
}

static BOOL safe_windivert_send(HANDLE handle, void* packet, UINT packet_len, UINT* send_len, void* addr) {
    return WinDivertSend(handle, packet, packet_len, send_len, (PWINDIVERT_ADDRESS)addr);
}

static void safe_windivert_close(HANDLE handle) {
    WinDivertClose(handle);
}
*/
import "C"

import (
	"fmt"
	"log"
	"time"
	"unsafe"
)

// 使用更简单的过滤器进行测试
const filter = "tcp"

// PacketMetadata 包含元信息
type PacketMetadata struct {
	SrcIP   string
	DstIP   string
	SrcPort uint16
	DstPort uint16
}

// Start 拦截主循环
func Start(handlePacket func([]byte, *PacketMetadata)) {
	log.Printf("尝试打开WinDivert，过滤器: %s", filter)

	// 创建过滤器字符串的C字符串
	filterCStr := C.CString(filter)
	defer C.free(unsafe.Pointer(filterCStr))

	// 使用包装函数打开WinDivert
	handle := C.safe_windivert_open(filterCStr, C.WINDIVERT_LAYER_NETWORK, 0, 0)
	if handle == nil {
		lastError := getLastError()
		log.Fatalf("WinDivertOpen 失败，错误码: %d\n请确保：\n1. 以管理员权限运行\n2. WinDivert驱动已正确安装\n3. 系统支持WinDivert", lastError)
	}
	defer C.safe_windivert_close(handle)

	log.Printf("✅ WinDivert打开成功，过滤器: %s", filter)
	log.Println("开始监听网络流量...")
	log.Println("请在另一个终端执行网络操作来测试拦截功能")

	buf := make([]byte, 65535)
	packetCount := 0
	errorCount := 0
	lastLogTime := time.Now()

	for {
		var addr C.WINDIVERT_ADDRESS
		var recvLen C.UINT

		// 使用包装函数接收数据包
		result := C.safe_windivert_recv(handle, unsafe.Pointer(&buf[0]), C.UINT(len(buf)), &recvLen, unsafe.Pointer(&addr))
		if result == 0 {
			// 失败时获取具体错误信息
			lastError := getLastError()
			errorCount++

			// 限制错误日志频率，避免刷屏
			now := time.Now()
			if errorCount == 1 || now.Sub(lastLogTime) > 5*time.Second {
				log.Printf("WinDivertRecv 失败，错误码: %d，累计错误次数: %d", lastError, errorCount)
				lastLogTime = now

				// 提供错误码的详细说明
				switch lastError {
				case 6: // ERROR_INVALID_HANDLE
					log.Printf("错误：无效句柄")
					return // 句柄无效，直接退出
				case 122: // ERROR_INSUFFICIENT_BUFFER
					log.Printf("错误：缓冲区不足")
				case 995: // ERROR_OPERATION_ABORTED
					log.Printf("错误：操作被中止")
				case 258: // ERROR_NO_MORE_ITEMS / WAIT_TIMEOUT
					// 这是正常的超时，继续等待
					continue
				default:
					log.Printf("错误：未知错误码 %d", lastError)
				}
			}

			// 如果错误太多，稍微休息一下
			if errorCount%1000 == 0 {
				time.Sleep(10 * time.Millisecond)
			}
			continue
		}

		// 重置错误计数
		if errorCount > 0 {
			log.Printf("✅ 开始接收数据包，之前累计错误: %d 次", errorCount)
			errorCount = 0
		}

		packetCount++
		log.Printf("🎯 成功拦截到第 %d 个数据包！大小: %d 字节", packetCount, recvLen)

		// 复制数据包内容，避免并发问题
		packetData := make([]byte, recvLen)
		copy(packetData, buf[:recvLen])

		// 解析IP头部信息
		meta := parsePacketInfo(packetData, &addr)
		log.Printf("📦 数据包信息: %s:%d -> %s:%d", meta.SrcIP, meta.SrcPort, meta.DstIP, meta.DstPort)

		// 异步处理数据包分析
		go func(data []byte, metadata *PacketMetadata) {
			handlePacket(data, metadata)
		}(packetData, meta)

		// 重要：将数据包重新发送回网络栈，确保网络正常工作
		var sendLen C.UINT
		sendResult := C.safe_windivert_send(handle, unsafe.Pointer(&packetData[0]), C.UINT(len(packetData)), &sendLen, unsafe.Pointer(&addr))
		if sendResult == 0 {
			sendError := getLastError()
			log.Printf("⚠️ 警告：数据包转发失败，错误码: %d", sendError)
		} else {
			log.Printf("✅ 数据包已转发，发送字节数: %d", sendLen)
		}
	}
}

// parsePacketInfo 解析数据包的IP和端口信息
func parsePacketInfo(packet []byte, addr *C.WINDIVERT_ADDRESS) *PacketMetadata {
	meta := &PacketMetadata{
		SrcIP:   "unknown",
		DstIP:   "unknown",
		SrcPort: 0,
		DstPort: 0,
	}

	if len(packet) < 20 {
		return meta
	}

	// 简单解析IP头部（前20字节）
	if len(packet) >= 20 {
		// IP头部格式：版本(4bit) + 头长度(4bit) + 服务类型(8bit) + 总长度(16bit) + ...
		// 源IP地址在偏移12-15字节，目标IP在偏移16-19字节
		srcIP := (uint32(packet[12]) << 24) | (uint32(packet[13]) << 16) | (uint32(packet[14]) << 8) | uint32(packet[15])
		dstIP := (uint32(packet[16]) << 24) | (uint32(packet[17]) << 16) | (uint32(packet[18]) << 8) | uint32(packet[19])

		meta.SrcIP = formatIPv4(srcIP)
		meta.DstIP = formatIPv4(dstIP)

		// 获取IP头部长度
		ipHeaderLen := int(packet[0]&0x0F) * 4

		// 检查是否是TCP协议 (协议号6)
		if len(packet) > 9 && packet[9] == 6 && len(packet) >= ipHeaderLen+4 {
			// TCP头部：源端口(16bit) + 目标端口(16bit) + ...
			tcpStart := ipHeaderLen
			if len(packet) >= tcpStart+4 {
				meta.SrcPort = (uint16(packet[tcpStart]) << 8) | uint16(packet[tcpStart+1])
				meta.DstPort = (uint16(packet[tcpStart+2]) << 8) | uint16(packet[tcpStart+3])
			}
		}
	}

	return meta
}

// formatIPv4 将32位整数转换为IP地址字符串
func formatIPv4(addr uint32) string {
	return fmt.Sprintf("%d.%d.%d.%d",
		(addr)&0xFF,
		(addr>>8)&0xFF,
		(addr>>16)&0xFF,
		(addr>>24)&0xFF)
}

// getLastError 获取Windows最后的错误码
func getLastError() uint32 {
	return uint32(C.GetLastError())
}
