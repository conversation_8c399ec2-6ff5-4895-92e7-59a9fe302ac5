package main

/*
#cgo CFLAGS: -I${SRCDIR}/lib
#cgo LDFLAGS: -L${SRCDIR}/lib -lwindivert
#include "windivert.h"
#include <windows.h>
#include <stdio.h>

// 简单的测试函数
void test_windivert() {
    printf("Testing WinDivert...\n");

    // 尝试打开一个简单的句柄
    HANDLE handle = WinDivertOpen("tcp", WINDIVERT_LAYER_NETWORK, 0, 0);
    if (handle == INVALID_HANDLE_VALUE) {
        DWORD error = GetLastError();
        printf("WinDivertOpen failed with error: %lu\n", error);
        return;
    }

    printf("WinDivertOpen succeeded!\n");

    // 尝试接收一个数据包（非阻塞）
    char buffer[1500];
    UINT recv_len;
    WINDIVERT_ADDRESS addr;

    // 设置超时
    if (!WinDivertSetParam(handle, WINDIVERT_PARAM_QUEUE_TIME, 1000)) {
        printf("Failed to set timeout\n");
    }

    BOOL result = WinDivertRecv(handle, buffer, sizeof(buffer), &recv_len, &addr);
    if (!result) {
        DWORD error = GetLastError();
        printf("WinDivertRecv failed with error: %lu\n", error);
        if (error == ERROR_NO_MORE_ITEMS || error == WAIT_TIMEOUT) {
            printf("This is normal - no packets to receive\n");
        }
    } else {
        printf("WinDivertRecv succeeded! Received %u bytes\n", recv_len);
    }

    WinDivertClose(handle);
    printf("Test completed\n");
}
*/
import "C"

import (
	"fmt"
	"log"
)

func main() {
	fmt.Println("开始测试WinDivert...")

	// 调用C函数进行测试
	C.test_windivert()

	log.Println("测试完成")
}
