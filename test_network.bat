@echo off
title 网络测试脚本
echo ========================================
echo           网络连接测试
echo ========================================
echo.
echo 此脚本将生成一些网络流量来测试拦截器
echo 请确保网络拦截器已在另一个管理员权限窗口中运行
echo.
pause
echo.
echo 开始测试网络连接...
echo.

echo 1. 测试 DNS 查询...
nslookup ad.oceanengine.com
echo.

echo 2. 测试 HTTP 连接 (包含目标域名)...
echo 尝试连接 ad.oceanengine.com...
curl -v -H "Host: ad.oceanengine.com" -H "User-Agent: TestAgent" "http://ad.oceanengine.com/" --connect-timeout 5 --max-time 10 2>&1 | findstr /i "host\|connect\|error" || echo "连接尝试完成"
echo.

echo 3. 使用 PowerShell 发送 HTTP 请求...
powershell -Command "try { $response = Invoke-WebRequest -Uri 'http://ad.oceanengine.com' -TimeoutSec 5 -ErrorAction Stop; Write-Host '请求成功' } catch { Write-Host '请求失败，但已发送HTTP请求' }"
echo.

echo 4. 测试其他包含关键词的HTTP请求...
echo 发送到百度的请求（应该不匹配）...
curl -s -H "User-Agent: TestAgent" "http://www.baidu.com" --connect-timeout 3 --max-time 5 >nul 2>&1 || echo "百度请求完成"
echo.

echo 5. 使用 telnet 发送原始HTTP请求...
echo 这将发送包含目标域名的原始HTTP请求
(
echo GET / HTTP/1.1
echo Host: ad.oceanengine.com
echo User-Agent: TestClient/1.0
echo Connection: close
echo.
) | telnet ad.oceanengine.com 80 2>nul || echo "Telnet请求已发送"
echo.

echo 6. 生成更多包含关键词的流量...
echo 使用 telnet 模拟 HTTP 请求...
echo GET / HTTP/1.1 > temp_request.txt
echo Host: ad.oceanengine.com >> temp_request.txt
echo User-Agent: TestClient >> temp_request.txt
echo. >> temp_request.txt
echo 已创建包含目标域名的HTTP请求文件
type temp_request.txt
del temp_request.txt 2>nul
echo.

echo ========================================
echo 网络测试完成！
echo 请检查拦截器窗口是否有捕获到相关流量
echo 特别关注包含 "ad.oceanengine.com" 的HTTP请求
echo ========================================
pause
