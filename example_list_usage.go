package main

import (
	"log"

	"net-interceptor/model"
)

// 这是一个示例文件，展示如何使用新的 List 方法
func main() {
	// 初始化数据库
	db, err := model.InitDB("packets.db")
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 示例1: 查询所有数据（不分页）
	log.Println("=== 示例1: 查询所有数据（不分页） ===")
	req1 := &model.PacketListRequest{}
	packets, total, err := packetModel.List(req1)
	if err != nil {
		log.Printf("查询失败: %v", err)
	} else {
		log.Printf("查询成功，返回 %d 条数据，total: %d", len(packets), total)
	}

	// 示例2: 分页查询
	log.Println("\n=== 示例2: 分页查询（第1页，每页10条） ===")
	req2 := &model.PacketListRequest{
		Page:     1,
		PageSize: 10,
	}
	packets, total, err = packetModel.List(req2)
	if err != nil {
		log.Printf("分页查询失败: %v", err)
	} else {
		log.Printf("分页查询成功，返回 %d 条数据，total: %d", len(packets), total)
	}

	// 示例3: 条件查询
	log.Println("\n=== 示例3: 条件查询（根据源IP） ===")
	srcIp := "*************"
	req3 := &model.PacketListRequest{
		SrcIp: &srcIp,
	}
	packets, total, err = packetModel.List(req3)
	if err != nil {
		log.Printf("条件查询失败: %v", err)
	} else {
		log.Printf("条件查询成功，SrcIp=%s，返回 %d 条数据，total: %d", srcIp, len(packets), total)
	}

	// 示例4: 模糊查询 + 分页
	log.Println("\n=== 示例4: 模糊查询 + 分页 ===")
	payload := "HTTP"
	req4 := &model.PacketListRequest{
		Payload:  &payload,
		Page:     1,
		PageSize: 5,
	}
	packets, total, err = packetModel.List(req4)
	if err != nil {
		log.Printf("模糊查询失败: %v", err)
	} else {
		log.Printf("模糊查询成功，Payload包含'%s'，返回 %d 条数据，total: %d", payload, len(packets), total)
	}

	// 示例5: 多条件查询
	log.Println("\n=== 示例5: 多条件查询 ===")
	dstPort := uint16(80)
	req5 := &model.PacketListRequest{
		SrcIp:   &srcIp,
		DstPort: &dstPort,
		Page:    1,
		PageSize: 20,
	}
	packets, total, err = packetModel.List(req5)
	if err != nil {
		log.Printf("多条件查询失败: %v", err)
	} else {
		log.Printf("多条件查询成功，SrcIp=%s AND DstPort=%d，返回 %d 条数据，total: %d", 
			srcIp, dstPort, len(packets), total)
	}

	log.Println("\n✅ List方法使用示例完成")
}
