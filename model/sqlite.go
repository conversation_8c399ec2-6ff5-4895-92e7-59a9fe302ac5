package model

import (
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Packet 数据包模型
type Packet struct {
	Id        int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID" json:"id"`            // 数据库主键ID
	SrcIp     string    `gorm:"column:src_ip;comment:源IP地址" json:"src_ip"`                               // 源IP地址
	DstIp     string    `gorm:"column:dst_ip;comment:目标IP地址" json:"dst_ip"`                              // 目标IP地址
	SrcPort   uint16    `gorm:"column:src_port;comment:源端口" json:"src_port"`                             // 源端口
	DstPort   uint16    `gorm:"column:dst_port;comment:目标端口" json:"dst_port"`                            // 目标端口
	Payload   string    `gorm:"column:payload;comment:数据包内容" json:"payload"`                             // 数据包内容
	Timestamp time.Time `gorm:"column:timestamp;default:CURRENT_TIMESTAMP;comment:时间戳" json:"timestamp"` // 时间戳
}

// TableName 指定表名
func (Packet) TableName() string {
	return "packets"
}

// PacketModel 数据包模型
type PacketModel struct {
	db *gorm.DB
}

// NewPacketModel 创建数据包模型
func NewPacketModel(db *gorm.DB) *PacketModel {
	return &PacketModel{
		db: db,
	}
}

// InitDB 初始化数据库连接
func InitDB(path string) (*gorm.DB, error) {
	db, err := gorm.Open(sqlite.Open(path), &gorm.Config{})
	if err != nil {
		return nil, err
	}

	// 自动迁移表结构
	if err := db.AutoMigrate(&Packet{}); err != nil {
		return nil, err
	}

	return db, nil
}

// Create 创建数据包记录
func (m *PacketModel) Create(packet *Packet) error {
	return m.db.Create(packet).Error
}

// Update 更新数据包记录
func (m *PacketModel) Update(tx *gorm.DB, packet *Packet) error {
	db := m.db
	if tx != nil {
		db = tx
	}
	return db.Updates(packet).Error
}

// FindById 根据ID查找数据包
func (m *PacketModel) FindById(id int64) (*Packet, error) {
	var packet Packet
	err := m.db.Where("id = ?", id).First(&packet).Error
	if err != nil {
		return nil, err
	}
	return &packet, nil
}

// PacketReq 数据包列表查询请求参数
type PacketReq struct {
	Packet
	Page     int `json:"page"`      // 页码，从1开始
	PageSize int `json:"page_size"` // 每页大小
}

// List 根据条件查询数据包列表
func (m *PacketModel) List(req PacketReq) ([]*Packet, int64, error) {
	var packets []*Packet
	var total int64

	query := m.db.Model(&Packet{})

	// 构建查询条件（使用零值判断）
	if req.Id != 0 {
		query = query.Where("id = ?", req.Id)
	}
	if req.SrcIp != "" {
		query = query.Where("src_ip = ?", req.SrcIp)
	}
	if req.DstIp != "" {
		query = query.Where("dst_ip = ?", req.DstIp)
	}
	if req.SrcPort != 0 {
		query = query.Where("src_port = ?", req.SrcPort)
	}
	if req.DstPort != 0 {
		query = query.Where("dst_port = ?", req.DstPort)
	}
	if req.Payload != "" {
		query = query.Where("payload LIKE ?", "%"+req.Payload+"%")
	}
	if !req.Timestamp.IsZero() {
		query = query.Where("timestamp = ?", req.Timestamp)
	}
	// 分页处理
	if req.Page > 0 && req.PageSize > 0 {
		// 获取总数
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		query = query.Offset((req.Page - 1) * req.PageSize).Limit(req.PageSize)
	}

	// 执行查询
	if err := query.Find(&packets).Error; err != nil {
		return nil, 0, err
	}

	// 如果不分页，返回实际数据长度作为total
	if req.Page <= 0 || req.PageSize <= 0 {
		total = int64(len(packets))
	}

	return packets, total, nil
}

// Delete 删除数据包记录
func (m *PacketModel) Delete(id int64) error {
	return m.db.Where("id = ?", id).Delete(&Packet{}).Error
}
