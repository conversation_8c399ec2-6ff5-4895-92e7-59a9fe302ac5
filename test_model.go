package main

import (
	"log"
	"time"

	"net-interceptor/model"
)

func main() {
	// 测试数据库连接和模型
	db, err := model.InitDB("test_packets.db")
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 创建测试数据包
	testPacket := &model.Packet{
		SrcIp:     "*************",
		DstIp:     "*******",
		SrcPort:   12345,
		DstPort:   80,
		Payload:   "GET / HTTP/1.1\r\nHost: example.com\r\n\r\n",
		Timestamp: time.Now(),
	}

	// 保存数据包
	if err := packetModel.Create(testPacket); err != nil {
		log.Fatalf("创建数据包失败: %v", err)
	}

	log.Printf("✅ 数据包创建成功，ID: %d", testPacket.Id)

	// 查询数据包
	foundPacket, err := packetModel.FindById(testPacket.Id)
	if err != nil {
		log.Fatalf("查询数据包失败: %v", err)
	}

	log.Printf("✅ 查询成功: %s:%d -> %s:%d, Payload: %s", 
		foundPacket.SrcIp, foundPacket.SrcPort, 
		foundPacket.DstIp, foundPacket.DstPort, 
		foundPacket.Payload)

	// 按条件查询
	condition := map[string]interface{}{
		"src_ip": "*************",
	}
	packets, err := packetModel.FindByCondition(condition, 10, 0)
	if err != nil {
		log.Fatalf("条件查询失败: %v", err)
	}

	log.Printf("✅ 条件查询成功，找到 %d 个数据包", len(packets))

	log.Println("🎉 所有测试通过！GORM 模型工作正常")
}
